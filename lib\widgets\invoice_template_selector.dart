/// واجهة اختيار قوالب طباعة الفواتير
/// توفر واجهة سهلة لاختيار وتخصيص قوالب الطباعة المختلفة
library;

import 'package:flutter/material.dart';
import '../models/invoice.dart';
import '../models/invoice_template.dart';
import '../services/invoice_template_service.dart';
import '../services/enhanced_invoice_print_service.dart';
import '../constants/app_colors.dart';
import '../widgets/loading_widget.dart';

class InvoiceTemplateSelector extends StatefulWidget {
  final Invoice invoice;
  final Function(InvoiceTemplate)? onTemplateSelected;
  final bool showPreview;

  const InvoiceTemplateSelector({
    super.key,
    required this.invoice,
    this.onTemplateSelected,
    this.showPreview = true,
  });

  @override
  State<InvoiceTemplateSelector> createState() => _InvoiceTemplateSelectorState();
}

class _InvoiceTemplateSelectorState extends State<InvoiceTemplateSelector> {
  final InvoiceTemplateService _templateService = InvoiceTemplateService();
  
  List<InvoiceTemplate> _templates = [];
  InvoiceTemplate? _selectedTemplate;
  bool _isLoading = true;
  bool _isPrinting = false;

  @override
  void initState() {
    super.initState();
    _loadTemplates();
  }

  Future<void> _loadTemplates() async {
    setState(() => _isLoading = true);

    try {
      final templates = await _templateService.getActiveTemplates();
      final defaultTemplate = await _templateService.getDefaultTemplate();

      setState(() {
        _templates = templates;
        _selectedTemplate = defaultTemplate ?? (templates.isNotEmpty ? templates.first : null);
        _isLoading = false;
      });

      // إشعار الوالد بالقالب المحدد
      if (_selectedTemplate != null && widget.onTemplateSelected != null) {
        widget.onTemplateSelected!(_selectedTemplate!);
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل القوالب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _printWithSelectedTemplate() async {
    if (_selectedTemplate == null) return;

    setState(() => _isPrinting = true);

    try {
      await EnhancedInvoicePrintService.printInvoiceWithTemplate(
        context,
        widget.invoice,
        template: _selectedTemplate,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم فتح معاينة الطباعة بنجاح'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في طباعة الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isPrinting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: LoadingWidget());
    }

    if (_templates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.description_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'لا توجد قوالب متاحة',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadTemplates,
              child: const Text('إعادة تحميل'),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Icon(Icons.palette, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'اختيار قالب الطباعة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_isPrinting)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                ElevatedButton.icon(
                  onPressed: _selectedTemplate != null ? _printWithSelectedTemplate : null,
                  icon: const Icon(Icons.print),
                  label: const Text('طباعة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                ),
            ],
          ),
        ),

        // قائمة القوالب
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _templates.length,
            itemBuilder: (context, index) {
              final template = _templates[index];
              final isSelected = _selectedTemplate?.id == template.id;

              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                elevation: isSelected ? 4 : 1,
                color: isSelected ? AppColors.primary.withOpacity(0.1) : null,
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _selectedTemplate = template;
                    });
                    if (widget.onTemplateSelected != null) {
                      widget.onTemplateSelected!(template);
                    }
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        // أيقونة القالب
                        Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: _getTemplateColor(template.type),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            _getTemplateIcon(template.type),
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                        const SizedBox(width: 16),

                        // معلومات القالب
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    template.name,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: isSelected ? AppColors.primary : null,
                                    ),
                                  ),
                                  if (template.isDefault) ...[
                                    const SizedBox(width: 8),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: AppColors.success,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: const Text(
                                        'افتراضي',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                template.type.description,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  _buildTemplateFeature(
                                    'الألوان',
                                    _getColorDescription(template.colors),
                                  ),
                                  const SizedBox(width: 16),
                                  _buildTemplateFeature(
                                    'الخط',
                                    '${template.fonts.headerSize.toInt()}pt',
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // مؤشر التحديد
                        if (isSelected)
                          const Icon(
                            Icons.check_circle,
                            color: AppColors.primary,
                            size: 24,
                          )
                        else
                          Icon(
                            Icons.radio_button_unchecked,
                            color: Colors.grey[400],
                            size: 24,
                          ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        // معاينة القالب (اختيارية)
        if (widget.showPreview && _selectedTemplate != null)
          _buildTemplatePreview(_selectedTemplate!),
      ],
    );
  }

  Widget _buildTemplateFeature(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildTemplatePreview(InvoiceTemplate template) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.visibility, size: 20),
              const SizedBox(width: 8),
              const Text(
                'معاينة القالب',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  // فتح معاينة مفصلة
                  _showDetailedPreview(template);
                },
                child: const Text('معاينة مفصلة'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            height: 120,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _getTemplateIcon(template.type),
                    size: 40,
                    color: _getTemplateColor(template.type),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'معاينة ${template.name}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    'فاتورة ${widget.invoice.invoiceNumber}',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDetailedPreview(InvoiceTemplate template) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('معاينة ${template.name}'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('نوع القالب: ${template.type.displayName}'),
              const SizedBox(height: 8),
              Text('الوصف: ${template.type.description}'),
              const SizedBox(height: 16),
              const Text('الألوان:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildColorSample('أساسي', template.colors.primary),
                  const SizedBox(width: 8),
                  _buildColorSample('ثانوي', template.colors.secondary),
                  const SizedBox(width: 8),
                  _buildColorSample('مميز', template.colors.accent),
                ],
              ),
              const SizedBox(height: 16),
              const Text('الخطوط:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text('حجم العنوان: ${template.fonts.headerSize.toInt()}pt'),
              Text('حجم النص: ${template.fonts.bodySize.toInt()}pt'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _printWithSelectedTemplate();
            },
            child: const Text('طباعة'),
          ),
        ],
      ),
    );
  }

  Widget _buildColorSample(String label, dynamic color) {
    return Column(
      children: [
        Container(
          width: 30,
          height: 30,
          decoration: BoxDecoration(
            color: Colors.blue, // سيتم تحسين هذا لاحقاً لعرض اللون الفعلي
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.grey),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 10),
        ),
      ],
    );
  }

  Color _getTemplateColor(InvoiceTemplateType type) {
    switch (type) {
      case InvoiceTemplateType.classic:
        return Colors.blue;
      case InvoiceTemplateType.modern:
        return Colors.purple;
      case InvoiceTemplateType.minimal:
        return Colors.grey;
      case InvoiceTemplateType.luxury:
        return Colors.amber;
      case InvoiceTemplateType.custom:
        return Colors.green;
    }
  }

  IconData _getTemplateIcon(InvoiceTemplateType type) {
    switch (type) {
      case InvoiceTemplateType.classic:
        return Icons.article;
      case InvoiceTemplateType.modern:
        return Icons.auto_awesome;
      case InvoiceTemplateType.minimal:
        return Icons.minimize;
      case InvoiceTemplateType.luxury:
        return Icons.diamond;
      case InvoiceTemplateType.custom:
        return Icons.tune;
    }
  }

  String _getColorDescription(TemplateColors colors) {
    // وصف مبسط للألوان
    return 'متنوعة';
  }
}
