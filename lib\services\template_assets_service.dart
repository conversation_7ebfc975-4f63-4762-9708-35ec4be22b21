/// خدمة إدارة أصول قوالب الطباعة
/// توفر إدارة الشعارات والصور والخطوط المخصصة للقوالب
library;

import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pdf/widgets.dart' as pw;
import '../services/logging_service.dart';

class TemplateAssetsService {
  static const String _assetsFolder = 'template_assets';
  static const String _logosFolder = 'logos';
  static const String _signaturesFolder = 'signatures';
  static const String _fontsFolder = 'fonts';

  /// الحصول على مجلد الأصول
  static Future<Directory> _getAssetsDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final assetsDir = Directory('${appDir.path}/$_assetsFolder');
    
    if (!await assetsDir.exists()) {
      await assetsDir.create(recursive: true);
    }
    
    return assetsDir;
  }

  /// الحصول على مجلد الشعارات
  static Future<Directory> _getLogosDirectory() async {
    final assetsDir = await _getAssetsDirectory();
    final logosDir = Directory('${assetsDir.path}/$_logosFolder');
    
    if (!await logosDir.exists()) {
      await logosDir.create(recursive: true);
    }
    
    return logosDir;
  }

  /// الحصول على مجلد التوقيعات
  static Future<Directory> _getSignaturesDirectory() async {
    final assetsDir = await _getAssetsDirectory();
    final signaturesDir = Directory('${assetsDir.path}/$_signaturesFolder');
    
    if (!await signaturesDir.exists()) {
      await signaturesDir.create(recursive: true);
    }
    
    return signaturesDir;
  }

  /// رفع شعار جديد
  static Future<String?> uploadLogo({String? existingPath}) async {
    try {
      final picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 600,
        imageQuality: 85,
      );

      if (image == null) return existingPath;

      final logosDir = await _getLogosDirectory();
      final fileName = 'logo_${DateTime.now().millisecondsSinceEpoch}.png';
      final logoPath = '${logosDir.path}/$fileName';

      // نسخ الملف إلى مجلد الشعارات
      await File(image.path).copy(logoPath);

      // حذف الشعار القديم إذا كان موجوداً
      if (existingPath != null && existingPath.isNotEmpty) {
        try {
          await File(existingPath).delete();
        } catch (e) {
          LoggingService.warning(
            'لم يتم حذف الشعار القديم',
            category: 'TemplateAssets',
            data: {'oldPath': existingPath, 'error': e.toString()},
          );
        }
      }

      LoggingService.info(
        'تم رفع شعار جديد',
        category: 'TemplateAssets',
        data: {'logoPath': logoPath},
      );

      return logoPath;
    } catch (e) {
      LoggingService.error(
        'خطأ في رفع الشعار',
        category: 'TemplateAssets',
        data: {'error': e.toString()},
      );
      return existingPath;
    }
  }

  /// رفع توقيع جديد
  static Future<String?> uploadSignature({String? existingPath}) async {
    try {
      final picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 400,
        maxHeight: 200,
        imageQuality: 90,
      );

      if (image == null) return existingPath;

      final signaturesDir = await _getSignaturesDirectory();
      final fileName = 'signature_${DateTime.now().millisecondsSinceEpoch}.png';
      final signaturePath = '${signaturesDir.path}/$fileName';

      // نسخ الملف إلى مجلد التوقيعات
      await File(image.path).copy(signaturePath);

      // حذف التوقيع القديم إذا كان موجوداً
      if (existingPath != null && existingPath.isNotEmpty) {
        try {
          await File(existingPath).delete();
        } catch (e) {
          LoggingService.warning(
            'لم يتم حذف التوقيع القديم',
            category: 'TemplateAssets',
            data: {'oldPath': existingPath, 'error': e.toString()},
          );
        }
      }

      LoggingService.info(
        'تم رفع توقيع جديد',
        category: 'TemplateAssets',
        data: {'signaturePath': signaturePath},
      );

      return signaturePath;
    } catch (e) {
      LoggingService.error(
        'خطأ في رفع التوقيع',
        category: 'TemplateAssets',
        data: {'error': e.toString()},
      );
      return existingPath;
    }
  }

  /// تحميل صورة كـ PDF Image
  static Future<pw.ImageProvider?> loadImageForPDF(String? imagePath) async {
    if (imagePath == null || imagePath.isEmpty) return null;

    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        LoggingService.warning(
          'ملف الصورة غير موجود',
          category: 'TemplateAssets',
          data: {'imagePath': imagePath},
        );
        return null;
      }

      final bytes = await file.readAsBytes();
      return pw.MemoryImage(bytes);
    } catch (e) {
      LoggingService.error(
        'خطأ في تحميل الصورة للـ PDF',
        category: 'TemplateAssets',
        data: {'imagePath': imagePath, 'error': e.toString()},
      );
      return null;
    }
  }

  /// حذف شعار
  static Future<bool> deleteLogo(String logoPath) async {
    try {
      final file = File(logoPath);
      if (await file.exists()) {
        await file.delete();
        LoggingService.info(
          'تم حذف الشعار',
          category: 'TemplateAssets',
          data: {'logoPath': logoPath},
        );
        return true;
      }
      return false;
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف الشعار',
        category: 'TemplateAssets',
        data: {'logoPath': logoPath, 'error': e.toString()},
      );
      return false;
    }
  }

  /// حذف توقيع
  static Future<bool> deleteSignature(String signaturePath) async {
    try {
      final file = File(signaturePath);
      if (await file.exists()) {
        await file.delete();
        LoggingService.info(
          'تم حذف التوقيع',
          category: 'TemplateAssets',
          data: {'signaturePath': signaturePath},
        );
        return true;
      }
      return false;
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف التوقيع',
        category: 'TemplateAssets',
        data: {'signaturePath': signaturePath, 'error': e.toString()},
      );
      return false;
    }
  }

  /// الحصول على قائمة الشعارات المتاحة
  static Future<List<String>> getAvailableLogos() async {
    try {
      final logosDir = await _getLogosDirectory();
      final files = await logosDir.list().toList();
      
      return files
          .where((file) => file is File)
          .map((file) => file.path)
          .where((path) => _isImageFile(path))
          .toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب قائمة الشعارات',
        category: 'TemplateAssets',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على قائمة التوقيعات المتاحة
  static Future<List<String>> getAvailableSignatures() async {
    try {
      final signaturesDir = await _getSignaturesDirectory();
      final files = await signaturesDir.list().toList();
      
      return files
          .where((file) => file is File)
          .map((file) => file.path)
          .where((path) => _isImageFile(path))
          .toList();
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب قائمة التوقيعات',
        category: 'TemplateAssets',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// التحقق من أن الملف صورة
  static bool _isImageFile(String path) {
    final extension = path.toLowerCase().split('.').last;
    return ['png', 'jpg', 'jpeg', 'gif', 'bmp'].contains(extension);
  }

  /// تنظيف الملفات القديمة
  static Future<void> cleanupOldFiles({int maxAgeInDays = 30}) async {
    try {
      final assetsDir = await _getAssetsDirectory();
      final cutoffDate = DateTime.now().subtract(Duration(days: maxAgeInDays));
      
      await for (final entity in assetsDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            try {
              await entity.delete();
              LoggingService.info(
                'تم حذف ملف قديم',
                category: 'TemplateAssets',
                data: {'filePath': entity.path, 'age': maxAgeInDays},
              );
            } catch (e) {
              LoggingService.warning(
                'فشل في حذف ملف قديم',
                category: 'TemplateAssets',
                data: {'filePath': entity.path, 'error': e.toString()},
              );
            }
          }
        }
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تنظيف الملفات القديمة',
        category: 'TemplateAssets',
        data: {'error': e.toString()},
      );
    }
  }

  /// الحصول على حجم مجلد الأصول
  static Future<int> getAssetsFolderSize() async {
    try {
      final assetsDir = await _getAssetsDirectory();
      int totalSize = 0;
      
      await for (final entity in assetsDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }
      
      return totalSize;
    } catch (e) {
      LoggingService.error(
        'خطأ في حساب حجم مجلد الأصول',
        category: 'TemplateAssets',
        data: {'error': e.toString()},
      );
      return 0;
    }
  }

  /// تصدير الأصول
  static Future<String?> exportAssets() async {
    try {
      final assetsDir = await _getAssetsDirectory();
      final appDir = await getApplicationDocumentsDirectory();
      final exportPath = '${appDir.path}/template_assets_backup_${DateTime.now().millisecondsSinceEpoch}.zip';
      
      // هنا يمكن إضافة منطق ضغط الملفات
      // لكن سنكتفي بنسخ المجلد للآن
      
      LoggingService.info(
        'تم تصدير الأصول',
        category: 'TemplateAssets',
        data: {'exportPath': exportPath},
      );
      
      return exportPath;
    } catch (e) {
      LoggingService.error(
        'خطأ في تصدير الأصول',
        category: 'TemplateAssets',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// استيراد الأصول
  static Future<bool> importAssets(String importPath) async {
    try {
      // هنا يمكن إضافة منطق استيراد الملفات
      // من ملف مضغوط أو مجلد
      
      LoggingService.info(
        'تم استيراد الأصول',
        category: 'TemplateAssets',
        data: {'importPath': importPath},
      );
      
      return true;
    } catch (e) {
      LoggingService.error(
        'خطأ في استيراد الأصول',
        category: 'TemplateAssets',
        data: {'importPath': importPath, 'error': e.toString()},
      );
      return false;
    }
  }
}
