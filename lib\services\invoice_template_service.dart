/// خدمة إدارة قوالب طباعة الفواتير الاحترافية
/// توفر إدارة شاملة لقوالب الطباعة المختلفة مع دعم التخصيص
library;

import 'dart:convert';
import '../models/invoice_template.dart';
import '../helpers/database_helper.dart';
import '../constants/app_constants.dart';
import '../services/logging_service.dart';
import '../services/audit_service.dart';

class InvoiceTemplateService {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  /// الحصول على جميع القوالب
  Future<List<InvoiceTemplate>> getAllTemplates() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        AppConstants.invoiceTemplatesTable,
        where: 'is_active = 1',
        orderBy: 'is_default DESC, name ASC',
      );

      return List.generate(maps.length, (i) {
        return InvoiceTemplate.fromMap(_parseJsonFields(maps[i]));
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب قوالب الطباعة',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على القوالب النشطة فقط
  Future<List<InvoiceTemplate>> getActiveTemplates() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        AppConstants.invoiceTemplatesTable,
        where: 'is_active = 1',
        orderBy: 'is_default DESC, name ASC',
      );

      return List.generate(maps.length, (i) {
        return InvoiceTemplate.fromMap(_parseJsonFields(maps[i]));
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب القوالب النشطة',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString()},
      );
      return [];
    }
  }

  /// الحصول على قالب بالمعرف
  Future<InvoiceTemplate?> getTemplateById(int id) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        AppConstants.invoiceTemplatesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isNotEmpty) {
        return InvoiceTemplate.fromMap(_parseJsonFields(maps.first));
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب القالب',
        category: 'InvoiceTemplateService',
        data: {'id': id, 'error': e.toString()},
      );
      return null;
    }
  }

  /// الحصول على القالب الافتراضي
  Future<InvoiceTemplate?> getDefaultTemplate() async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        AppConstants.invoiceTemplatesTable,
        where: 'is_default = 1 AND is_active = 1',
        limit: 1,
      );

      if (maps.isNotEmpty) {
        return InvoiceTemplate.fromMap(_parseJsonFields(maps.first));
      }

      // إذا لم يوجد قالب افتراضي، إنشاء قالب كلاسيكي افتراضي
      final defaultTemplate = InvoiceTemplate.createDefault(
        InvoiceTemplateType.classic,
      );
      await createTemplate(defaultTemplate);
      return defaultTemplate;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب القالب الافتراضي',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString()},
      );
      return null;
    }
  }

  /// إنشاء قالب جديد
  Future<int> createTemplate(InvoiceTemplate template) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من عدم تكرار الاسم
      final existingTemplate = await getTemplateByName(template.name);
      if (existingTemplate != null) {
        throw Exception('اسم القالب موجود مسبقاً');
      }

      // إذا كان القالب افتراضي، إلغاء الافتراضية من القوالب الأخرى
      if (template.isDefault) {
        await _clearDefaultTemplates();
      }

      final templateData = _stringifyJsonFields(template.toMap());
      final id = await db.insert(
        AppConstants.invoiceTemplatesTable,
        templateData,
      );

      // تسجيل العملية في سجل المراجعة
      await AuditService.logCreate(
        entityType: AppConstants.auditEntityTemplate,
        entityId: id,
        entityName: template.name,
        newValues: templateData,
        description: 'تم إنشاء قالب طباعة جديد',
        category: 'InvoiceTemplate',
      );

      LoggingService.info(
        'تم إنشاء قالب طباعة جديد',
        category: 'InvoiceTemplateService',
        data: {'templateId': id, 'templateName': template.name},
      );

      return id;
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء قالب الطباعة',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString(), 'templateName': template.name},
      );
      rethrow;
    }
  }

  /// تحديث قالب موجود
  Future<int> updateTemplate(InvoiceTemplate template) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود القالب
      final existingTemplate = await getTemplateById(template.id!);
      if (existingTemplate == null) {
        throw Exception('القالب غير موجود');
      }

      // التحقق من عدم تكرار الاسم مع قوالب أخرى
      final templateWithSameName = await getTemplateByName(template.name);
      if (templateWithSameName != null &&
          templateWithSameName.id != template.id) {
        throw Exception('اسم القالب موجود مسبقاً');
      }

      // إذا كان القالب افتراضي، إلغاء الافتراضية من القوالب الأخرى
      if (template.isDefault && !existingTemplate.isDefault) {
        await _clearDefaultTemplates();
      }

      final templateData = _stringifyJsonFields(
        template.copyWith(updatedAt: DateTime.now()).toMap(),
      );

      final result = await db.update(
        AppConstants.invoiceTemplatesTable,
        templateData,
        where: 'id = ?',
        whereArgs: [template.id],
      );

      // تسجيل العملية في سجل المراجعة
      await AuditService.logUpdate(
        entityType: AppConstants.auditEntityTemplate,
        entityId: template.id!,
        entityName: template.name,
        oldValues: _stringifyJsonFields(existingTemplate.toMap()),
        newValues: templateData,
        description: 'تم تحديث قالب الطباعة',
        category: 'InvoiceTemplate',
      );

      LoggingService.info(
        'تم تحديث قالب الطباعة',
        category: 'InvoiceTemplateService',
        data: {'templateId': template.id, 'templateName': template.name},
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في تحديث قالب الطباعة',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString(), 'templateId': template.id},
      );
      rethrow;
    }
  }

  /// حذف قالب
  Future<int> deleteTemplate(int id) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود القالب
      final template = await getTemplateById(id);
      if (template == null) {
        throw Exception('القالب غير موجود');
      }

      // منع حذف القالب الافتراضي إذا كان الوحيد
      if (template.isDefault) {
        final activeTemplates = await getActiveTemplates();
        if (activeTemplates.length <= 1) {
          throw Exception('لا يمكن حذف القالب الافتراضي الوحيد');
        }
      }

      final result = await db.delete(
        AppConstants.invoiceTemplatesTable,
        where: 'id = ?',
        whereArgs: [id],
      );

      // إذا تم حذف القالب الافتراضي، تعيين قالب آخر كافتراضي
      if (template.isDefault) {
        await _setFirstTemplateAsDefault();
      }

      // تسجيل العملية في سجل المراجعة
      await AuditService.logDelete(
        entityType: AppConstants.auditEntityTemplate,
        entityId: id,
        entityName: template.name,
        oldValues: _stringifyJsonFields(template.toMap()),
        description: 'تم حذف قالب الطباعة',
        category: 'InvoiceTemplate',
      );

      LoggingService.info(
        'تم حذف قالب الطباعة',
        category: 'InvoiceTemplateService',
        data: {'templateId': id, 'templateName': template.name},
      );

      return result;
    } catch (e) {
      LoggingService.error(
        'خطأ في حذف قالب الطباعة',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString(), 'templateId': id},
      );
      rethrow;
    }
  }

  /// الحصول على قالب بالاسم
  Future<InvoiceTemplate?> getTemplateByName(String name) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        AppConstants.invoiceTemplatesTable,
        where: 'name = ?',
        whereArgs: [name],
      );

      if (maps.isNotEmpty) {
        return InvoiceTemplate.fromMap(_parseJsonFields(maps.first));
      }
      return null;
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث عن القالب بالاسم',
        category: 'InvoiceTemplateService',
        data: {'name': name, 'error': e.toString()},
      );
      return null;
    }
  }

  /// تعيين قالب كافتراضي
  Future<void> setAsDefault(int templateId) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود القالب
      final template = await getTemplateById(templateId);
      if (template == null) {
        throw Exception('القالب غير موجود');
      }

      // إلغاء الافتراضية من جميع القوالب
      await _clearDefaultTemplates();

      // تعيين القالب المحدد كافتراضي
      await db.update(
        AppConstants.invoiceTemplatesTable,
        {'is_default': 1, 'updated_at': DateTime.now().toIso8601String()},
        where: 'id = ?',
        whereArgs: [templateId],
      );

      LoggingService.info(
        'تم تعيين قالب افتراضي جديد',
        category: 'InvoiceTemplateService',
        data: {'templateId': templateId, 'templateName': template.name},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تعيين القالب الافتراضي',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString(), 'templateId': templateId},
      );
      rethrow;
    }
  }

  /// تفعيل/إلغاء تفعيل قالب
  Future<void> toggleTemplateStatus(int templateId, bool isActive) async {
    try {
      final db = await _databaseHelper.database;

      // التحقق من وجود القالب
      final template = await getTemplateById(templateId);
      if (template == null) {
        throw Exception('القالب غير موجود');
      }

      // منع إلغاء تفعيل القالب الافتراضي إذا كان الوحيد النشط
      if (!isActive && template.isDefault) {
        final activeTemplates = await getActiveTemplates();
        if (activeTemplates.length <= 1) {
          throw Exception('لا يمكن إلغاء تفعيل القالب الافتراضي الوحيد');
        }
      }

      await db.update(
        AppConstants.invoiceTemplatesTable,
        {
          'is_active': isActive ? 1 : 0,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [templateId],
      );

      // إذا تم إلغاء تفعيل القالب الافتراضي، تعيين قالب آخر كافتراضي
      if (!isActive && template.isDefault) {
        await _setFirstTemplateAsDefault();
      }

      LoggingService.info(
        'تم ${isActive ? 'تفعيل' : 'إلغاء تفعيل'} القالب',
        category: 'InvoiceTemplateService',
        data: {'templateId': templateId, 'isActive': isActive},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في تغيير حالة القالب',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString(), 'templateId': templateId},
      );
      rethrow;
    }
  }

  /// إنشاء القوالب الافتراضية
  Future<void> createDefaultTemplates() async {
    try {
      LoggingService.info(
        'بدء إنشاء القوالب الافتراضية',
        category: 'InvoiceTemplateService',
      );

      // التحقق من وجود قوالب
      final existingTemplates = await getAllTemplates();
      if (existingTemplates.isNotEmpty) {
        LoggingService.info(
          'القوالب موجودة مسبقاً',
          category: 'InvoiceTemplateService',
          data: {'count': existingTemplates.length},
        );
        return;
      }

      // إنشاء القوالب الافتراضية
      final templates = [
        InvoiceTemplate.createDefault(InvoiceTemplateType.classic),
        InvoiceTemplate.createDefault(
          InvoiceTemplateType.modern,
        ).copyWith(name: 'قالب حديث', isDefault: false),
        InvoiceTemplate.createDefault(
          InvoiceTemplateType.minimal,
        ).copyWith(name: 'قالب مبسط', isDefault: false),
        InvoiceTemplate.createDefault(
          InvoiceTemplateType.luxury,
        ).copyWith(name: 'قالب فاخر', isDefault: false),
      ];

      for (final template in templates) {
        await createTemplate(template);
      }

      LoggingService.info(
        'تم إنشاء القوالب الافتراضية بنجاح',
        category: 'InvoiceTemplateService',
        data: {'count': templates.length},
      );
    } catch (e) {
      LoggingService.error(
        'خطأ في إنشاء القوالب الافتراضية',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString()},
      );
      rethrow;
    }
  }

  /// نسخ قالب موجود
  Future<int> duplicateTemplate(int templateId, String newName) async {
    try {
      final originalTemplate = await getTemplateById(templateId);
      if (originalTemplate == null) {
        throw Exception('القالب الأصلي غير موجود');
      }

      // التحقق من عدم تكرار الاسم الجديد
      final existingTemplate = await getTemplateByName(newName);
      if (existingTemplate != null) {
        throw Exception('اسم القالب الجديد موجود مسبقاً');
      }

      // إنشاء نسخة من القالب
      final duplicatedTemplate = originalTemplate.copyWith(
        id: null,
        name: newName,
        isDefault: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final newId = await createTemplate(duplicatedTemplate);

      LoggingService.info(
        'تم نسخ القالب بنجاح',
        category: 'InvoiceTemplateService',
        data: {'originalId': templateId, 'newId': newId, 'newName': newName},
      );

      return newId;
    } catch (e) {
      LoggingService.error(
        'خطأ في نسخ القالب',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString(), 'templateId': templateId},
      );
      rethrow;
    }
  }

  /// الحصول على إحصائيات القوالب
  Future<Map<String, dynamic>> getTemplateStatistics() async {
    try {
      final db = await _databaseHelper.database;

      final result = await db.rawQuery('''
        SELECT
          COUNT(*) as total_templates,
          COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_templates,
          COUNT(CASE WHEN is_default = 1 THEN 1 END) as default_templates,
          COUNT(CASE WHEN type = 'classic' THEN 1 END) as classic_templates,
          COUNT(CASE WHEN type = 'modern' THEN 1 END) as modern_templates,
          COUNT(CASE WHEN type = 'minimal' THEN 1 END) as minimal_templates,
          COUNT(CASE WHEN type = 'luxury' THEN 1 END) as luxury_templates,
          COUNT(CASE WHEN type = 'custom' THEN 1 END) as custom_templates
        FROM ${AppConstants.invoiceTemplatesTable}
      ''');

      return result.first;
    } catch (e) {
      LoggingService.error(
        'خطأ في جلب إحصائيات القوالب',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString()},
      );
      return {};
    }
  }

  /// البحث في القوالب
  Future<List<InvoiceTemplate>> searchTemplates(String query) async {
    try {
      final db = await _databaseHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        AppConstants.invoiceTemplatesTable,
        where: 'name LIKE ? AND is_active = 1',
        whereArgs: ['%$query%'],
        orderBy: 'is_default DESC, name ASC',
      );

      return List.generate(maps.length, (i) {
        return InvoiceTemplate.fromMap(_parseJsonFields(maps[i]));
      });
    } catch (e) {
      LoggingService.error(
        'خطأ في البحث في القوالب',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString(), 'query': query},
      );
      return [];
    }
  }

  /// إلغاء الافتراضية من جميع القوالب
  Future<void> _clearDefaultTemplates() async {
    final db = await _databaseHelper.database;
    await db.update(AppConstants.invoiceTemplatesTable, {
      'is_default': 0,
      'updated_at': DateTime.now().toIso8601String(),
    }, where: 'is_default = 1');
  }

  /// تعيين أول قالب نشط كافتراضي
  Future<void> _setFirstTemplateAsDefault() async {
    final activeTemplates = await getActiveTemplates();
    if (activeTemplates.isNotEmpty) {
      await setAsDefault(activeTemplates.first.id!);
    }
  }

  /// تحويل الحقول JSON إلى نصوص للحفظ في قاعدة البيانات
  Map<String, dynamic> _stringifyJsonFields(Map<String, dynamic> data) {
    final result = Map<String, dynamic>.from(data);

    if (result['colors'] is Map) {
      result['colors'] = jsonEncode(result['colors']);
    }
    if (result['fonts'] is Map) {
      result['fonts'] = jsonEncode(result['fonts']);
    }
    if (result['layout'] is Map) {
      result['layout'] = jsonEncode(result['layout']);
    }
    if (result['company_settings'] is Map) {
      result['company_settings'] = jsonEncode(result['company_settings']);
    }
    if (result['advanced_settings'] is Map) {
      result['advanced_settings'] = jsonEncode(result['advanced_settings']);
    }

    return result;
  }

  /// تحويل النصوص JSON إلى كائنات للقراءة من قاعدة البيانات
  Map<String, dynamic> _parseJsonFields(Map<String, dynamic> data) {
    final result = Map<String, dynamic>.from(data);

    try {
      if (result['colors'] is String) {
        result['colors'] = jsonDecode(result['colors']);
      }
      if (result['fonts'] is String) {
        result['fonts'] = jsonDecode(result['fonts']);
      }
      if (result['layout'] is String) {
        result['layout'] = jsonDecode(result['layout']);
      }
      if (result['company_settings'] is String) {
        result['company_settings'] = jsonDecode(result['company_settings']);
      }
    } catch (e) {
      LoggingService.error(
        'خطأ في تحليل حقول JSON',
        category: 'InvoiceTemplateService',
        data: {'error': e.toString()},
      );
    }

    return result;
  }
}
